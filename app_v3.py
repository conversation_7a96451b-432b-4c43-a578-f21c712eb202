"""
智能财务数据分析平台 v3.0 - 基于PandasAI v3
严格遵循官方API规范: https://docs.pandas-ai.com/v3/introduction
"""
import streamlit as st
import pandas as pd
from typing import Optional, List
from config_v3 import ConfigV3
from core.analysis_engine_v3 import AnalysisEngineV3
from core.data_manager import DataManager
from utils.logger import setup_logger, get_logger

# 设置页面配置
st.set_page_config(
    page_title=ConfigV3.APP_TITLE,
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化日志
setup_logger()
logger = get_logger(__name__)

class DataAnalysisAppV3:
    """数据分析应用v3主类 - 基于PandasAI v3"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.analysis_engine = AnalysisEngineV3()
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        if 'current_file' not in st.session_state:
            st.session_state.current_file = None
        if 'data_loaded' not in st.session_state:
            st.session_state.data_loaded = False
        if 'current_dataset_name' not in st.session_state:
            st.session_state.current_dataset_name = None
        if 'dataset_type' not in st.session_state:
            st.session_state.dataset_type = "general"
        if 'show_welcome' not in st.session_state:
            st.session_state.show_welcome = True
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("📁 数据管理 v3.0")
        
        # 显示版本信息
        st.sidebar.info("🚀 基于PandasAI v3.0\n✨ 语义数据层支持")
        
        # 文件上传
        uploaded_file = st.sidebar.file_uploader(
            "上传数据文件",
            type=ConfigV3.SUPPORTED_FILE_TYPES,
            help=f"支持格式: {', '.join(ConfigV3.SUPPORTED_FILE_TYPES)}"
        )
        
        if uploaded_file is not None:
            if self.data_manager.save_uploaded_file(uploaded_file.getvalue(), uploaded_file.name):
                st.sidebar.success(f"✅ 文件 '{uploaded_file.name}' 已保存")
                st.rerun()
        
        # 文件选择
        uploaded_files = self.data_manager.get_uploaded_files()
        
        if uploaded_files:
            st.sidebar.subheader("📂 已上传的文件")
            selected_file = st.sidebar.selectbox(
                "选择要分析的文件",
                options=["请选择文件..."] + uploaded_files,
                index=0 if st.session_state.current_file is None else 
                      (uploaded_files.index(st.session_state.current_file) + 1 
                       if st.session_state.current_file in uploaded_files else 0)
            )
            
            if selected_file != "请选择文件..." and selected_file != st.session_state.current_file:
                self._load_file(selected_file)
        
        # 数据集类型选择
        if st.session_state.data_loaded:
            st.sidebar.subheader("🏷️ 数据集类型")
            dataset_types = {
                "general": "通用数据",
                "revenue": "收入数据", 
                "expenses": "支出数据",
                "balance_sheet": "资产负债表"
            }
            
            selected_type = st.sidebar.selectbox(
                "选择数据类型",
                options=list(dataset_types.keys()),
                format_func=lambda x: dataset_types[x],
                index=list(dataset_types.keys()).index(st.session_state.dataset_type)
            )
            
            if selected_type != st.session_state.dataset_type:
                st.session_state.dataset_type = selected_type
                # 重新加载数据以应用新的数据集类型
                if st.session_state.current_file:
                    self._load_file(st.session_state.current_file)
        
        # 语义数据集管理
        if st.session_state.data_loaded:
            st.sidebar.subheader("🧠 语义数据集")
            available_datasets = self.analysis_engine.get_available_datasets()
            
            if available_datasets:
                st.sidebar.write("已创建的数据集:")
                for dataset in available_datasets:
                    st.sidebar.write(f"• {dataset}")
            else:
                st.sidebar.info("暂无语义数据集")
        
        # LLM状态显示
        st.sidebar.subheader("🤖 AI状态")
        if self.analysis_engine.llm_configured:
            st.sidebar.success("✅ AI已就绪")
            st.sidebar.write(f"模型: {ConfigV3.LLM_MODEL}")
        else:
            st.sidebar.error("❌ AI未配置")
            st.sidebar.write("请检查API密钥设置")
    
    def _load_file(self, filename: str):
        """加载文件"""
        try:
            df, error = self.data_manager.load_data(filename)

            if error:
                st.sidebar.error(f"❌ {error}")
                return

            # 检查数据是否正确加载
            if df is None:
                st.sidebar.error("❌ 数据加载失败：返回空数据")
                return

            # 生成符合v3规范的数据集名称
            dataset_name = filename.split('.')[0].lower().replace('_', '-').replace(' ', '-')

            # 加载到分析引擎（使用重新加载方法避免路径冲突）
            if self.analysis_engine.reload_data(df, dataset_name, st.session_state.dataset_type):
                st.session_state.current_file = filename
                st.session_state.current_dataset_name = dataset_name
                st.session_state.data_loaded = True
                st.session_state.show_welcome = True
                
                # 清空对话历史
                st.session_state.messages = []
                
                # 添加欢迎消息
                welcome_msg = self._generate_welcome_message(df, dataset_name)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": welcome_msg
                })

                # 验证数据是否正确保存到分析引擎
                if self.analysis_engine.current_data is None:
                    logger.error("数据未正确保存到分析引擎")
                    st.sidebar.error("⚠️ 数据保存异常，请重试")
                else:
                    logger.info(f"数据验证成功，形状: {self.analysis_engine.current_data.shape}")
                
                st.sidebar.success("✅ 数据加载成功")
                logger.info(f"文件加载成功: {filename}")
                st.rerun()
            else:
                st.sidebar.error("❌ 数据加载失败")
                
        except Exception as e:
            error_msg = f"加载文件时出错: {str(e)}"
            st.sidebar.error(f"❌ {error_msg}")
            logger.error(error_msg)
    
    def _generate_welcome_message(self, df: pd.DataFrame, dataset_name: str) -> str:
        """生成欢迎消息"""
        schema_info = ConfigV3.get_financial_schema(st.session_state.dataset_type)

        # 安全检查数据框
        if df is None:
            return "❌ 数据加载失败，请重新上传文件"

        try:
            shape_info = f"{df.shape[0]} 行 × {df.shape[1]} 列"
            columns_info = ', '.join(df.columns.tolist()[:5]) + ('...' if len(df.columns) > 5 else '')
        except Exception as e:
            shape_info = "未知"
            columns_info = "未知"

        return f"""
✅ **数据加载成功！**

📊 **数据概览:**
- 文件名: {st.session_state.current_file}
- 数据集: {dataset_name}
- 类型: {schema_info['description']}
- 形状: {shape_info}
- 列名: {columns_info}

🧠 **语义层已激活！**
- 智能数据理解
- 增强分析能力
- 多种输出格式

🤖 **AI分析已就绪！** 您可以开始提问了。
        """
    
    def render_main_content(self):
        """渲染主内容"""
        st.title(ConfigV3.APP_TITLE)
        
        if not st.session_state.data_loaded:
            self._render_welcome_screen()
            return
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["💬 AI对话", "📊 数据概览", "🧠 智能洞察", "⚙️ 系统状态"])
        
        with tab1:
            self._render_chat_interface()
        
        with tab2:
            self._render_data_overview()
        
        with tab3:
            self._render_insights_panel()
        
        with tab4:
            self._render_system_status()
    
    def _render_welcome_screen(self):
        """渲染欢迎屏幕"""
        st.markdown("""
        ## 🎉 欢迎使用智能财务数据分析平台 v3.0
        
        ### ✨ PandasAI v3 新功能亮点：
        - 🧠 **语义数据层**: 更智能的数据理解和上下文感知
        - 🚀 **增强AI分析**: 基于最新LLM技术的强大分析能力
        - 📊 **多种输出格式**: 文本、图表、数据表格等丰富展示
        - 🎯 **多数据集支持**: 跨数据集分析和关联查询
        - 🔧 **统一LLM接口**: 通过LiteLLM支持多种AI模型
        
        ### 🚀 开始使用：
        1. 👈 在左侧上传您的数据文件
        2. 🏷️ 选择合适的数据类型以启用语义层
        3. 💬 开始与您的数据对话！
        """)
        
        # 显示支持的数据类型
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            ### 📈 支持的数据类型：
            - **收入数据**: 销售、营收分析，智能趋势识别
            - **支出数据**: 成本、费用分析，异常检测  
            - **资产负债表**: 财务状况分析，比率计算
            - **通用数据**: 其他财务数据，灵活分析
            """)
        
        with col2:
            st.markdown("""
            ### 🎯 v3 AI分析能力：
            - 自然语言查询理解
            - 智能图表生成
            - 趋势分析和预测
            - 异常值自动检测
            - 跨数据集关联分析
            - 语义化数据解释
            """)
        
        # 显示示例数据
        if st.button("📋 查看示例数据格式"):
            st.markdown("### 💡 推荐的数据格式示例：")
            
            example_data = pd.DataFrame({
                'date': ['2024-01-01', '2024-01-02', '2024-01-03'],
                'revenue': [10000, 12000, 11000],
                'category': ['产品A', '产品B', '产品A'],
                'region': ['北京', '上海', '广州']
            })
            
            st.dataframe(example_data, use_container_width=True)
            st.caption("💡 包含日期、数值和分类字段的数据最适合AI分析")
    
    def run(self):
        """运行应用"""
        try:
            # 验证配置
            ConfigV3.validate_config()
            
            # 渲染界面
            self.render_sidebar()
            self.render_main_content()
            
        except Exception as e:
            st.error(f"应用启动失败: {str(e)}")
            logger.error(f"应用启动失败: {e}")

    def _render_chat_interface(self):
        """渲染聊天界面"""
        # 显示对话历史
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                self._render_message_content(message["content"])

        # 智能问题建议
        if st.session_state.show_welcome and len(st.session_state.messages) <= 1:
            self._render_question_suggestions()

        # 用户输入
        if prompt := st.chat_input("请输入您的问题..."):
            st.session_state.show_welcome = False
            self._handle_user_input(prompt)

    def _render_question_suggestions(self):
        """渲染智能问题建议"""
        st.markdown("### 💡 智能问题建议")
        st.markdown("基于您的数据类型，推荐以下问题：")

        suggestions = self._get_smart_suggestions()

        cols = st.columns(2)
        for i, suggestion in enumerate(suggestions[:6]):
            col = cols[i % 2]
            if col.button(f"📊 {suggestion}", key=f"suggestion_{i}"):
                st.session_state.show_welcome = False
                self._handle_user_input(suggestion)
                st.rerun()

    def _get_smart_suggestions(self) -> List[str]:
        """获取智能建议"""
        dataset_type = st.session_state.dataset_type

        suggestions_map = {
            "revenue": [
                "显示收入趋势图",
                "哪个产品收入最高？",
                "按月份分析收入变化",
                "计算收入增长率",
                "找出收入异常的时期",
                "预测下个月收入"
            ],
            "expenses": [
                "分析主要支出类别",
                "哪个部门支出最多？",
                "显示支出分布图",
                "找出支出异常值",
                "计算支出占比",
                "对比预算与实际支出"
            ],
            "balance_sheet": [
                "计算资产负债率",
                "显示资产结构图",
                "分析财务健康状况",
                "计算流动比率",
                "资产变化趋势",
                "负债结构分析"
            ],
            "general": [
                "显示数据概览",
                "生成统计摘要",
                "找出数据中的模式",
                "检测异常值",
                "显示相关性分析",
                "创建数据可视化"
            ]
        }

        return suggestions_map.get(dataset_type, suggestions_map["general"])

    def _render_data_overview(self):
        """渲染数据概览"""
        if not st.session_state.data_loaded:
            return

        df = self.analysis_engine.current_data

        # 检查数据是否存在
        if df is None:
            st.error("❌ 数据未正确加载，请重新上传文件")
            return

        # 基本信息卡片
        col1, col2, col3, col4 = st.columns(4)

        try:
            with col1:
                st.metric("总行数", f"{df.shape[0]:,}")
            with col2:
                st.metric("总列数", df.shape[1])
            with col3:
                memory_mb = df.memory_usage(deep=True).sum() / 1024**2
                st.metric("内存使用", f"{memory_mb:.1f} MB")
            with col4:
                missing_pct = (df.isnull().sum().sum() / (df.shape[0] * df.shape[1]) * 100)
                st.metric("缺失值比例", f"{missing_pct:.1f}%")
        except Exception as e:
            st.error(f"❌ 数据指标计算失败: {str(e)}")
            logger.error(f"数据指标计算失败: {e}")
            return

        # 数据预览
        st.subheader("📋 数据预览")
        st.dataframe(df.head(10), use_container_width=True)

        # 数据类型和统计信息
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("📊 数据类型")
            dtype_df = pd.DataFrame({
                '列名': df.columns,
                '数据类型': df.dtypes.astype(str),
                '非空值数量': df.count()
            })
            st.dataframe(dtype_df, use_container_width=True)

        with col2:
            st.subheader("📈 数值列统计")
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                st.dataframe(df[numeric_cols].describe(), use_container_width=True)
            else:
                st.info("没有数值列")

        # AI数据摘要
        if st.button("🤖 生成AI数据摘要"):
            with st.spinner("AI正在分析数据..."):
                summary = self.analysis_engine.get_data_summary()
                if summary["type"] != "error":
                    st.markdown("### 🧠 AI数据摘要")
                    st.write(summary["content"])
                else:
                    st.error(summary["content"])

    def _render_insights_panel(self):
        """渲染洞察面板"""
        st.subheader("🧠 AI数据洞察")
        st.markdown("利用PandasAI v3的强大能力，深度分析您的数据")

        # 快速分析按钮
        st.subheader("⚡ 快速分析")

        quick_analyses = [
            ("📊 数据分布分析", "分析所有数值列的分布情况"),
            ("📈 相关性分析", "计算变量之间的相关性"),
            ("🎯 异常值检测", "识别数据中的异常值"),
            ("📅 时间序列分析", "如果有日期列，分析时间趋势")
        ]

        cols = st.columns(2)
        for i, (title, query) in enumerate(quick_analyses):
            col = cols[i % 2]
            if col.button(title, key=f"quick_{i}"):
                self._handle_user_input(query)
                st.rerun()

        # 语义数据集信息
        st.subheader("🏗️ 语义数据集")
        datasets = self.analysis_engine.get_available_datasets()
        if datasets:
            st.success(f"已创建 {len(datasets)} 个语义数据集")
            for dataset in datasets:
                st.write(f"• {dataset}")
        else:
            st.info("暂无语义数据集，数据加载时会自动创建")

    def _render_system_status(self):
        """渲染系统状态"""
        st.subheader("⚙️ 系统状态")

        # LLM状态
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 🤖 AI模型状态")
            if self.analysis_engine.llm_configured:
                st.success("✅ AI模型已配置")
                st.write(f"**模型**: {ConfigV3.LLM_MODEL}")
                st.write(f"**温度**: {ConfigV3.LLM_TEMPERATURE}")
                st.write(f"**最大令牌**: {ConfigV3.LLM_MAX_TOKENS}")
            else:
                st.error("❌ AI模型未配置")
                st.write("请检查API密钥设置")

        with col2:
            st.markdown("### 📊 数据状态")
            if st.session_state.data_loaded:
                st.success("✅ 数据已加载")
                st.write(f"**文件**: {st.session_state.current_file}")
                st.write(f"**数据集**: {st.session_state.current_dataset_name}")
                st.write(f"**类型**: {st.session_state.dataset_type}")
            else:
                st.info("📁 请上传数据文件")

        # 测试连接
        if st.button("🔍 测试AI连接"):
            with st.spinner("测试中..."):
                result = self.analysis_engine.validate_llm_connection()
                if result["type"] == "error":
                    st.error(f"连接失败: {result['content']}")
                else:
                    st.success("连接成功！")

    def _render_message_content(self, content):
        """渲染消息内容"""
        if isinstance(content, str):
            st.markdown(content)
        elif isinstance(content, dict):
            if content.get('type') == 'dataframe':
                st.dataframe(content['content'], use_container_width=True)
                if 'metadata' in content:
                    metadata = content['metadata']
                    st.caption(f"形状: {metadata['shape'][0]} 行 × {metadata['shape'][1]} 列")
            elif content.get('type') == 'plot':
                st.pyplot(content['content'])
            elif content.get('type') == 'number':
                st.metric("结果", content.get('formatted', content['content']))
            elif content.get('type') == 'text':
                st.markdown(content['content'])
            elif content.get('type') == 'error':
                st.error(content['content'])
        else:
            st.write(content)

    def _handle_user_input(self, user_input: str):
        """处理用户输入"""
        # 添加用户消息
        st.session_state.messages.append({
            "role": "user",
            "content": user_input
        })

        # 显示用户消息
        with st.chat_message("user"):
            st.markdown(user_input)

        # 生成AI回复
        with st.chat_message("assistant"):
            with st.spinner("AI正在分析中..."):
                response = self._generate_response(user_input)
                self._render_message_content(response)

        # 添加AI回复到历史
        st.session_state.messages.append({
            "role": "assistant",
            "content": response
        })

    def _generate_response(self, user_input: str) -> dict:
        """生成AI回复"""
        try:
            # 使用语义数据集进行分析
            result = self.analysis_engine.analyze(
                user_input,
                st.session_state.current_dataset_name
            )
            return result

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            logger.error(error_msg)
            return {
                "type": "error",
                "content": error_msg
            }

def main():
    """主函数"""
    app = DataAnalysisAppV3()
    app.run()

if __name__ == "__main__":
    main()
